import json, random, pathlib, shutil
path = 'silkworm_all.json'
train_ratio, val_ratio = 0.8, 0.1  # test=残り
data = json.load(open(path))
ids = [img['id'] for img in data['images']]
random.shuffle(ids)
n=len(ids); n_train=int(n*train_ratio); n_val=int(n*val_ratio)
splits = dict(
    train=set(ids[:n_train]),
    val  =set(ids[n_train:n_train+n_val]),
    test =set(ids[n_train+n_val:])
)
for split in splits:
    out = {k:[] for k in data}
    for img in data['images']:
        if img['id'] in splits[split]:
            out['images'].append(img)
    keep = {ann['id']:ann for ann in data['annotations']
            if ann['image_id'] in splits[split]}
    out['annotations']=list(keep.values())
    out['categories']=data['categories']
    json.dump(out,open(f'{split}.json','w'))