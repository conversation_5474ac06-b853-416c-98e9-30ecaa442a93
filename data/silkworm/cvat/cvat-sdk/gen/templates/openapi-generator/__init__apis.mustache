{{#apiInfo}}
{{#apis}}
{{#-first}}

{{>partial_header}}

# Import all APIs into this package.
# If you have many APIs here with many many models used in each API this may
# raise a `RecursionError`.
# In order to avoid this, import only the API that you directly need like:
#
#   from {{packageName}}.api.{{classFilename}} import {{classname}}
#
# or import this package, but before doing it, use:
#
#   import sys
#   sys.setrecursionlimit(n)

# Import APIs into API package:
{{/-first}}
from {{apiPackage}}.{{classFilename}} import {{classname}}
{{/apis}}
{{/apiInfo}}
