name: Trigger dependent repo update workflow

on:
  workflow_dispatch:
  push:
    branches: [ master ]

jobs:
  trigger:
    runs-on: ubuntu-latest
    steps:
      - name: Generate authentication token
        id: gen-token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ secrets.CVAT_BOT_APP_ID }}
          private-key: ${{ secrets.CVAT_BOT_PRIVATE_KEY }}
          repositories: ${{ secrets.CVAT_DEPENDENT_REPO }}

      - name: Trigger private repository workflow
        env:
          GH_TOKEN: "${{ steps.gen-token.outputs.token }}"
          GH_REPO: cvat-ai/${{ secrets.CVAT_DEPENDENT_REPO }}
        run: |
          gh workflow run \
            update-dependency.yml \
            -f triggeredBy=${{ github.repository }} \
            -f commitSha=${{ github.sha }} \
            -f branch=${{ github.ref }}
