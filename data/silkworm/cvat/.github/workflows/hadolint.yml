name: HadoLint
on: pull_request
jobs:
  Linter:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Run checks
        env:
          HADOLINT: "${{ github.workspace }}/hadolint"
          HADOLINT_VER: "2.12.0"
          VERIFICATION_LEVEL: "error"
        run: |
          curl -sLo "$HADOLINT" "https://github.com/hadolint/hadolint/releases/download/v$HADOLINT_VER/hadolint-Linux-x86_64"
          chmod +x "$HADOLINT"
          echo "hadolint version: $("$HADOLINT" --version)"

          git ls-files -z 'Dockerfile*' '*/Dockerfile*' | xargs -0 "$HADOLINT" -t "$VERIFICATION_LEVEL"
