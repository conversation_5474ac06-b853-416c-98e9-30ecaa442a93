name: Pylint
on: pull_request
jobs:
  Linter:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Run checks
        run: |
          pipx install $(grep "^pylint==" ./dev/requirements.txt)

          pipx inject pylint \
            $(grep "^pylint-.\+==" ./dev/requirements.txt) \
            $(grep "^django==" ./cvat/requirements/base.txt)

          echo "Pylint version: "$(pylint --version | head -1)
          pylint -j0 .
