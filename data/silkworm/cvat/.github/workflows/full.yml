name: Full
on:
  workflow_call:
    inputs:
      ref:
        type: string
        required: true
  workflow_dispatch:
    inputs:
      ref:
        type: string
        required: true

env:
  WORKFLOW_RUN_URL: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}
  CYPRESS_VERIFY_TIMEOUT: 180000 # https://docs.cypress.io/guides/guides/command-line#cypress-verify
  CVAT_VERSION: "local"

jobs:
  search_cache:
    uses: ./.github/workflows/search-cache.yml

  build:
    needs: search_cache
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ inputs.ref }}

      - name: CVAT server. Getting cache from the default branch
        uses: actions/cache@v4
        with:
          path: /tmp/cvat_cache_server
          key: ${{ runner.os }}-build-server-${{ needs.search_cache.outputs.sha }}

      - name: CVAT UI. Getting cache from the default branch
        uses: actions/cache@v4
        with:
          path: /tmp/cvat_cache_ui
          key: ${{ runner.os }}-build-ui-${{ needs.search_cache.outputs.sha }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Create artifact directories
        run: |
          mkdir /tmp/cvat_server
          mkdir /tmp/cvat_ui
          mkdir /tmp/cvat_sdk

      - name: CVAT server. Build and push
        uses: docker/build-push-action@v6
        with:
          cache-from: type=local,src=/tmp/cvat_cache_server
          context: .
          file: Dockerfile
          tags: cvat/server:${{ env.CVAT_VERSION }}
          outputs: type=docker,dest=/tmp/cvat_server/image.tar

      - name: CVAT UI. Build and push
        uses: docker/build-push-action@v6
        with:
          cache-from: type=local,src=/tmp/cvat_cache_ui
          context: .
          file: Dockerfile.ui
          tags: cvat/ui:${{ env.CVAT_VERSION }}
          outputs: type=docker,dest=/tmp/cvat_ui/image.tar

      - name: CVAT SDK. Build
        run: |
          pip3 install --user -r cvat-sdk/gen/requirements.txt
          ./cvat-sdk/gen/generate.sh

          cp -r cvat-sdk/* /tmp/cvat_sdk/

      - name: Upload CVAT server artifact
        uses: actions/upload-artifact@v4
        with:
          name: cvat_server
          path: /tmp/cvat_server/image.tar

      - name: Upload CVAT UI artifact
        uses: actions/upload-artifact@v4
        with:
          name: cvat_ui
          path: /tmp/cvat_ui/image.tar

      - name: Upload CVAT SDK artifact
        uses: actions/upload-artifact@v4
        with:
          name: cvat_sdk
          path: /tmp/cvat_sdk/

  rest_api_testing:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ inputs.ref }}

      - uses: actions/setup-python@v5
        with:
          python-version: '3.9'

      - name: Download CVAT server image
        uses: actions/download-artifact@v4
        with:
          name: cvat_server
          path: /tmp/cvat_server/

      - name: Download CVAT UI images
        uses: actions/download-artifact@v4
        with:
          name: cvat_ui
          path: /tmp/cvat_ui/

      - name: Download CVAT SDK package
        uses: actions/download-artifact@v4
        with:
          name: cvat_sdk
          path: /tmp/cvat_sdk/

      - name: Load Docker images
        run: |
          docker load --input /tmp/cvat_server/image.tar
          docker load --input /tmp/cvat_ui/image.tar
          docker image ls -a

      - name: Verify API schema
        id: verify_schema
        run: |
          docker run --rm cvat/server:${CVAT_VERSION} bash \
            -c 'python manage.py spectacular' > cvat/schema-expected.yml

          if ! git diff --no-index cvat/schema.yml cvat/schema-expected.yml; then
            echo
            echo 'API schema has changed! Please update cvat/schema.yml:'
            echo
            echo '  docker run --rm cvat/server:dev bash \'
            echo "    -c 'python manage.py spectacular' > cvat/schema.yml"
            exit 1
          fi

      - name: Verify migrations
        run: |
          docker run --rm cvat/server:${CVAT_VERSION} bash \
            -c 'python manage.py makemigrations --check'

      - name: Generate SDK
        run: |
          pip3 install -r cvat-sdk/gen/requirements.txt
          ./cvat-sdk/gen/generate.sh

      - name: Install SDK
        run: |
          pip3 install -r ./tests/python/requirements.txt \
            -e './cvat-sdk[masks,pytorch]' -e ./cvat-cli \
            --extra-index-url https://download.pytorch.org/whl/cpu

      - name: Running REST API and SDK tests
        id: run_tests
        run: |
          pytest tests/python/
          ONE_RUNNING_JOB_IN_QUEUE_PER_USER="true" pytest tests/python/rest_api/test_queues.py
          CVAT_ALLOW_STATIC_CACHE="true" pytest -k "TestTaskData" tests/python

      - name: Creating a log file from cvat containers
        if: failure() && steps.run_tests.conclusion == 'failure'
        env:
          LOGS_DIR: "${{ github.workspace }}/rest_api_testing"
        run: |
            mkdir $LOGS_DIR
            docker logs test_cvat_server_1 > $LOGS_DIR/cvat_server.log
            docker logs test_cvat_worker_export_1 > $LOGS_DIR/cvat_worker_export.log
            docker logs test_cvat_worker_import_1 > $LOGS_DIR/cvat_worker_import.log
            docker logs test_cvat_opa_1 2> $LOGS_DIR/cvat_opa.log

      - name: Uploading "cvat" container logs as an artifact
        if: failure() && steps.run_tests.conclusion == 'failure'
        uses: actions/upload-artifact@v4
        with:
          name: rest_api_container_logs
          path: "${{ github.workspace }}/rest_api_testing"

  unit_testing:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ inputs.ref }}

      - name: Download CVAT server image
        uses: actions/download-artifact@v4
        with:
          name: cvat_server
          path: /tmp/cvat_server/

      - name: Load Docker server image
        run: |
          docker load --input /tmp/cvat_server/image.tar
          docker image ls -a

      - name: Running OPA tests
        run: |
          python cvat/apps/iam/rules/tests/generate_tests.py

          docker compose run --rm -v "$PWD:/mnt/src:ro" -w /mnt/src \
            cvat_opa test cvat/apps/*/rules

      - name: Running unit tests
        env:
          HOST_COVERAGE_DATA_DIR: ${{ github.workspace }}
          CONTAINER_COVERAGE_DATA_DIR: "/coverage_data"
        run: |
          docker compose -f docker-compose.yml -f docker-compose.dev.yml up -d cvat_opa cvat_server cvat_db

          max_tries=12
          while [[  $(curl -s -o /dev/null -w "%{http_code}" localhost:8181/health?bundles) != "200" && max_tries -gt 0 ]]; do (( max_tries-- )); sleep 5; done

          docker compose -f docker-compose.yml -f docker-compose.dev.yml -f docker-compose.ci.yml run cvat_ci /bin/bash \
            -c 'python manage.py test cvat/apps -v 2'

      - name: Creating a log file from cvat containers
        if: failure()
        env:
          LOGS_DIR: "${{ github.workspace }}/unit_testing"
        run: |
            mkdir $LOGS_DIR
            docker logs cvat_server > $LOGS_DIR/cvat_server.log
            docker logs cvat_opa 2> $LOGS_DIR/cvat_opa.log

      - name: Uploading "cvat" container logs as an artifact
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: unit_tests_container_logs
          path: "${{ github.workspace }}/unit_testing"

  e2e_testing:
    needs: build
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        specs: ['actions_tasks', 'actions_tasks2', 'actions_tasks3',
                'actions_objects', 'actions_objects2', 'actions_users',
                'actions_projects_models', 'canvas3d_functionality', 'canvas3d_functionality_2',
                'issues_prs', 'issues_prs2', 'features']
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ inputs.ref }}

      - uses: actions/setup-node@v4
        with:
            node-version: '22.x'

      - name: Download CVAT server image
        uses: actions/download-artifact@v4
        with:
          name: cvat_server
          path: /tmp/cvat_server/

      - name: Download CVAT UI image
        uses: actions/download-artifact@v4
        with:
          name: cvat_ui
          path: /tmp/cvat_ui/

      - name: Load Docker images
        run: |
          docker load --input /tmp/cvat_server/image.tar
          docker load --input /tmp/cvat_ui/image.tar
          docker image ls -a

      - name: Run CVAT instance
        run: |
          docker compose \
            -f docker-compose.yml \
            -f docker-compose.dev.yml \
            -f components/serverless/docker-compose.serverless.yml \
            -f tests/docker-compose.minio.yml \
            -f tests/docker-compose.file_share.yml up -d

      - name: Waiting for server
        env:
          API_ABOUT_PAGE: "localhost:8080/api/server/about"
        run: |
          max_tries=60
          status_code=$(curl -s -o /tmp/server_response -w "%{http_code}" ${API_ABOUT_PAGE})
          while [[  $status_code != "200" && max_tries -gt 0 ]]
          do
            echo Number of attempts left: $max_tries
            echo Status code of response: $status_code

            sleep 5
            status_code=$(curl -s -o /tmp/server_response -w "%{http_code}" ${API_ABOUT_PAGE})
            (( max_tries-- ))
          done

      - name: Run E2E tests
        env:
          DJANGO_SU_NAME: 'admin'
          DJANGO_SU_EMAIL: '<EMAIL>'
          DJANGO_SU_PASSWORD: '12qwaszx'
        run: |
          docker exec -i cvat_server /bin/bash -c "echo \"from django.contrib.auth.models import User; User.objects.create_superuser('${DJANGO_SU_NAME}', '${DJANGO_SU_EMAIL}', '${DJANGO_SU_PASSWORD}')\" | python3 ~/manage.py shell"
          cd ./tests
          yarn --frozen-lockfile

          if [[ ${{ matrix.specs }} == canvas3d_* ]]; then
            npx cypress run \
              --headed \
              --browser chrome \
              --env coverage=false \
              --config-file cypress_canvas3d.config.js \
              --spec 'cypress/e2e/${{ matrix.specs }}/**/*.js,cypress/e2e/remove_users_tasks_projects_organizations.js'
          else
            npx cypress run \
              --browser chrome \
              --env coverage=false \
              --spec 'cypress/e2e/${{ matrix.specs }}/**/*.js,cypress/e2e/remove_users_tasks_projects_organizations.js'
          fi

      - name: Creating a log file from "cvat" container logs
        if: failure()
        run: |
            docker logs cvat_server > ${{ github.workspace }}/tests/cvat_${{ matrix.specs }}.log

      - name: Uploading "cvat" container logs as an artifact
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: e2e_container_logs_${{ matrix.specs }}
          path: ${{ github.workspace }}/tests/cvat_${{ matrix.specs }}.log

      - name: Uploading cypress screenshots as an artifact
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: cypress_screenshots_${{ matrix.specs }}
          path: ${{ github.workspace }}/tests/cypress/screenshots
