name: "\U0001F680 Feature Request"
description: Suggest an idea for this project
labels: ["enhancement"]
body:
- type: checkboxes
  attributes:
    label: Actions before raising this issue
    options:
    - label: I searched the existing issues and did not find anything similar.
      required: true
    - label: I read/searched [the docs](https://docs.cvat.ai/docs/)
      required: true
- type: textarea
  attributes:
    label: Is your feature request related to a problem? Please describe.
    description: A clear and concise description of what the problem is.
    placeholder: I'm always frustrated when I have to press this small button.
  validations:
    required: false
- type: textarea
  attributes:
    label: Describe the solution you'd like
    description: A clear and concise description of what you want to happen.
    placeholder: Make this button bigger
  validations:
    required: false
- type: textarea
  attributes:
    label: Describe alternatives you've considered
    description: A clear and concise description of any alternative solutions or features you've considered.
    placeholder: I wanted to buy bigger display, but it would be too expensive.
  validations:
    required: false
- type: textarea
  attributes:
    label: Additional context
    description: |
      Add any other context or screenshots about the feature request here.
      Tip: You can attach images or log files by clicking this area to highlight it and then dragging files in.
  validations:
    required: false
