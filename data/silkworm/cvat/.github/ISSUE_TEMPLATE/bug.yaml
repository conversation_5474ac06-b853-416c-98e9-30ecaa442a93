name: "\U0001F41E Bug Report"
description: Create a bug report to help us fix it
labels: ["bug"]
body:
- type: checkboxes
  attributes:
    label: Actions before raising this issue
    options:
    - label: I searched the existing issues and did not find anything similar.
      required: true
    - label: I read/searched [the docs](https://docs.cvat.ai/docs/)
      required: true

- type: textarea
  attributes:
    label: Steps to Reproduce
    description: Provide a link to a live example or an unambiguous set of steps to reproduce this bug. Include code to reproduce, if relevant.
    placeholder: |
      1. Go to '...'
      2. Click on '....'
      3. Scroll down to '....'
      4. See error
  validations:
    required: false
- type: textarea
  attributes:
    label: Expected Behavior
    description: A concise description of what you expected to happen.
  validations:
    required: false
- type: textarea
  attributes:
    label: Possible Solution
    description: |
      Not obligatory, but suggest a fix/reason for the bug, or ideas on how to implement the addition or change
  validations:
    required: false
- type: textarea
  attributes:
    label: Context
    description: |
      How has this issue affected you? What are you trying to accomplish?
      Providing context helps us come up with a solution that is most useful in the real world!

      Tip: You can attach images or log files by clicking this area to highlight it and then dragging files in.
  validations:
    required: false
- type: textarea
  attributes:
    label: Environment
    description: |
      Include relevant details about the environment you experienced
    placeholder: |
      - Git hash commit (`git log -1`):
      - Docker version `docker version` (e.g. Docker 17.0.05):
      - Are you using Docker Swarm or Kubernetes?
      - Operating System and version (e.g. Linux, Windows, MacOS):
      - Code example or link to GitHub repo or gist to reproduce problem:
      - Other diagnostic information / logs:
        <details>
        <summary>Logs from `cvat` container</summary>
        </details>
    render: Markdown
  validations:
    required: false
