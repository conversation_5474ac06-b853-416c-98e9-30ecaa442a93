http:
  routers:
    grafana:
      entryPoints:
      - web
      middlewares:
      - analytics-auth
      - strip-prefix
      service: grafana
      rule: Host(`{{ env "CVAT_HOST" }}`) && PathPrefix(`/analytics`)
    grafana_https:
      entryPoints:
      - websecure
      middlewares:
      - analytics-auth
      - strip-prefix
      service: grafana
      tls: {}
      rule: Host(`{{ env "CVAT_HOST" }}`) && PathPrefix(`/analytics`)

  middlewares:
    analytics-auth:
      forwardauth:
        address: http://cvat_server:8080/analytics
        authRequestHeaders:
          - "<PERSON><PERSON>"
          - "Authorization"

    strip-prefix:
      stripprefix:
        prefixes:
        - /analytics

  services:
    grafana:
      loadBalancer:
        servers:
        - url: http://{{ env "DJANGO_LOG_VIEWER_HOST" }}:{{ env "DJANGO_LOG_VIEWER_PORT" }}
        passHostHeader: false
