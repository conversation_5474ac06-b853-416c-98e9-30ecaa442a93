The `*.txt` files in this directory are generated by `pip-compile-multi`
from the `*.in` files.

To regenerate them, first, install `pip-compile-multi` using the same Python
version as is used in the CVAT server Docker image. For example, you can
do it with `pipx` as follows:

    pipx install --python python3.XX pip-compile-multi

Now you can run the `regenerate.sh` script in this directory.
By default, it will update all dependencies to the latest allowed versions.
If needed, you can override this by passing additional `pip-compile-multi` options
to `regenerate.sh`, such as `--no-upgrade` or `--upgrade-package=<package>`.
