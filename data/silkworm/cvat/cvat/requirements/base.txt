# SHA1:8e74054e3bf63d97544ea03f3ba6df81b77e7cb1
#
# This file is automatically generated.
# To update it, refer to cvat/requirements/README.txt.
#
-r ../../utils/dataset_manifest/requirements.txt
asgiref==3.8.1
    # via django
async-timeout==5.0.1
    # via redis
attrs==21.4.0
    # via
    #   -r cvat/requirements/base.in
    #   datumaro
    #   jsonschema
azure-core==1.34.0
    # via
    #   azure-storage-blob
    #   msrest
azure-storage-blob==12.13.0
    # via -r cvat/requirements/base.in
boto3==1.39.0
    # via -r cvat/requirements/base.in
botocore==1.39.0
    # via
    #   boto3
    #   s3transfer
cachetools==5.5.2
    # via google-auth
certifi==2025.6.15
    # via
    #   clickhouse-connect
    #   msrest
    #   requests
cffi==1.17.1
    # via cryptography
charset-normalizer==3.4.2
    # via requests
click==8.2.1
    # via
    #   nltk
    #   rq
clickhouse-connect==0.6.8
    # via -r cvat/requirements/base.in
contourpy==1.2.1
    # via matplotlib
crontab==1.0.4
    # via rq-scheduler
cryptography==45.0.4
    # via
    #   azure-storage-blob
    #   datumaro
    #   pyjwt
cycler==0.12.1
    # via matplotlib
datumaro @ git+https://github.com/cvat-ai/datumaro.git@265bf06a4f742235828f9f53f7f7511a10217437
    # via -r cvat/requirements/base.in
defusedxml==0.7.1
    # via
    #   datumaro
    #   python3-openid
deprecated==1.2.18
    # via limits
dj-pagination==2.5.0
    # via -r cvat/requirements/base.in
dj-rest-auth[with-social]==5.0.2
    # via -r cvat/requirements/base.in
django==4.2.23
    # via
    #   -r cvat/requirements/base.in
    #   dj-rest-auth
    #   django-allauth
    #   django-appconf
    #   django-auth-ldap
    #   django-cors-headers
    #   django-crum
    #   django-filter
    #   django-health-check
    #   django-rq
    #   django-sendfile2
    #   djangorestframework
    #   drf-spectacular
django-allauth[saml]==0.57.2
    # via
    #   -r cvat/requirements/base.in
    #   dj-rest-auth
django-appconf==1.1.0
    # via django-compressor
django-auth-ldap==2.2.0
    # via -r cvat/requirements/base.in
django-compressor==4.3.1
    # via -r cvat/requirements/base.in
django-cors-headers==3.5.0
    # via -r cvat/requirements/base.in
django-crum==0.7.9
    # via -r cvat/requirements/base.in
django-filter==2.4.0
    # via -r cvat/requirements/base.in
django-health-check==3.20.0
    # via -r cvat/requirements/base.in
django-rq==2.8.1
    # via -r cvat/requirements/base.in
django-sendfile2==0.7.0
    # via -r cvat/requirements/base.in
djangorestframework==3.16.0
    # via
    #   -r cvat/requirements/base.in
    #   dj-rest-auth
    #   drf-spectacular
drf-spectacular==0.26.2
    # via -r cvat/requirements/base.in
easyprocess==1.1
    # via pyunpack
entrypoint2==1.1
    # via pyunpack
fonttools==4.58.4
    # via matplotlib
freezegun==1.5.2
    # via rq-scheduler
furl==2.1.0
    # via -r cvat/requirements/base.in
google-api-core==2.25.1
    # via
    #   google-cloud-core
    #   google-cloud-storage
google-auth==2.40.3
    # via
    #   google-api-core
    #   google-cloud-core
    #   google-cloud-storage
google-cloud-core==2.4.3
    # via google-cloud-storage
google-cloud-storage==1.42.0
    # via -r cvat/requirements/base.in
google-crc32c==1.7.1
    # via google-resumable-media
google-resumable-media==2.7.2
    # via google-cloud-storage
googleapis-common-protos==1.70.0
    # via google-api-core
h5py==3.14.0
    # via datumaro
idna==3.10
    # via requests
imagesize==1.4.1
    # via datumaro
importlib-metadata==8.7.0
    # via clickhouse-connect
importlib-resources==6.5.2
    # via nibabel
inflection==0.5.1
    # via drf-spectacular
isodate==0.7.2
    # via
    #   msrest
    #   python3-saml
jmespath==1.0.1
    # via
    #   boto3
    #   botocore
joblib==1.5.1
    # via
    #   nltk
    #   scikit-learn
json-stream==2.3.3
    # via -r cvat/requirements/base.in
json-stream-rs-tokenizer==0.4.29
    # via json-stream
jsonschema==4.17.3
    # via drf-spectacular
kiwisolver==1.4.8
    # via matplotlib
limits==5.4.0
    # via python-logstash-async
lxml==5.4.0
    # via
    #   -r cvat/requirements/base.in
    #   datumaro
    #   python3-saml
    #   xmlsec
lz4==4.4.4
    # via clickhouse-connect
matplotlib==3.8.4
    # via datumaro
mmh3==5.1.0
    # via pottery
msrest==0.7.1
    # via azure-storage-blob
networkx==3.4.2
    # via datumaro
nibabel==5.3.2
    # via datumaro
nltk==3.9.1
    # via datumaro
oauthlib==3.3.1
    # via requests-oauthlib
orderedmultidict==1.0.1
    # via furl
orjson==3.10.12
    # via datumaro
packaging==25.0
    # via
    #   limits
    #   matplotlib
    #   nibabel
    #   tensorboardx
pandas==2.3.0
    # via datumaro
patool==1.12
    # via -r cvat/requirements/base.in
pdf2image==1.14.0
    # via -r cvat/requirements/base.in
portalocker==3.2.0
    # via datumaro
pottery==3.0.1
    # via -r cvat/requirements/base.in
proto-plus==1.26.1
    # via google-api-core
protobuf==6.31.1
    # via
    #   datumaro
    #   google-api-core
    #   googleapis-common-protos
    #   proto-plus
    #   tensorboardx
psutil==5.9.4
    # via -r cvat/requirements/base.in
psycopg2-binary==2.9.5
    # via -r cvat/requirements/base.in
pyarrow==20.0.0
    # via datumaro
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   python-ldap
    #   rsa
pyasn1-modules==0.4.2
    # via
    #   google-auth
    #   python-ldap
pycocotools==2.0.10
    # via datumaro
pycparser==2.22
    # via cffi
pyemd==1.0.0
    # via datumaro
pyjwt[crypto]==2.10.1
    # via django-allauth
pylogbeat==2.0.1
    # via python-logstash-async
pyparsing==3.2.3
    # via matplotlib
pyrsistent==0.20.0
    # via jsonschema
python-dateutil==2.9.0.post0
    # via
    #   botocore
    #   freezegun
    #   matplotlib
    #   pandas
    #   rq-scheduler
python-ldap==3.4.3
    # via
    #   -r cvat/requirements/base.in
    #   django-auth-ldap
python-logstash-async==2.5.0
    # via -r cvat/requirements/base.in
python-rapidjson==1.20
    # via
    #   -r cvat/requirements/base.in
    #   datumaro
python3-openid==3.2.0
    # via django-allauth
python3-saml==1.16.0
    # via django-allauth
pytz==2025.2
    # via
    #   clickhouse-connect
    #   pandas
pyunpack==0.2.1
    # via -r cvat/requirements/base.in
pyyaml==6.0.2
    # via
    #   datumaro
    #   drf-spectacular
rcssmin==1.1.1
    # via django-compressor
redis==4.6.0
    # via
    #   -r cvat/requirements/base.in
    #   django-rq
    #   pottery
    #   rq
regex==2024.11.6
    # via nltk
requests==2.32.4
    # via
    #   -r cvat/requirements/base.in
    #   azure-core
    #   datumaro
    #   django-allauth
    #   google-api-core
    #   google-cloud-storage
    #   msrest
    #   python-logstash-async
    #   requests-oauthlib
requests-oauthlib==2.0.0
    # via
    #   django-allauth
    #   msrest
rjsmin==1.2.1
    # via django-compressor
rq==1.16.0
    # via
    #   -r cvat/requirements/base.in
    #   django-rq
    #   rq-scheduler
rq-scheduler==0.13.1
    # via -r cvat/requirements/base.in
rsa==4.9.1
    # via google-auth
ruamel-yaml==0.18.14
    # via datumaro
ruamel-yaml-clib==0.2.12
    # via ruamel-yaml
rules==3.5
    # via -r cvat/requirements/base.in
s3transfer==0.13.0
    # via boto3
scikit-learn==1.7.0
    # via datumaro
scipy==1.13.1
    # via
    #   datumaro
    #   scikit-learn
shapely==1.7.1
    # via
    #   -r cvat/requirements/base.in
    #   datumaro
six==1.17.0
    # via
    #   azure-core
    #   furl
    #   orderedmultidict
    #   python-dateutil
sqlparse==0.5.3
    # via django
tabulate==0.9.0
    # via datumaro
tensorboardx==2.6.4
    # via datumaro
threadpoolctl==3.6.0
    # via scikit-learn
typing-extensions==4.14.0
    # via
    #   asgiref
    #   azure-core
    #   datumaro
    #   limits
    #   nibabel
    #   pottery
tzdata==2025.2
    # via pandas
uritemplate==4.2.0
    # via drf-spectacular
urllib3==2.5.0
    # via
    #   botocore
    #   clickhouse-connect
    #   requests
wrapt==1.17.2
    # via deprecated
xmlsec==1.3.15
    # via
    #   -r cvat/requirements/base.in
    #   python3-saml
zipp==3.23.0
    # via importlib-metadata
zstandard==0.23.0
    # via clickhouse-connect
