Scope,Resource,Context,Ownership,Limit,Method,URL,Privilege,Membership
list,Invitation,Sandbox,N/A,,GET,/invitations,None,N/A
list,Invitation,Organization,N/A,,GET,/invitations,None,Worker
create,Invitation,Organization,N/A,"resource[""role""] not in [""maintainer"", ""owner""]",POST,/invitations,User,Maintainer
create,Invitation,Organization,N/A,"resource[""role""] != ""owner""",POST,/invitations,User,Owner
view,Invitation,Sandbox,None,,GET,/invitations/{id},Admin,N/A
view,Invitation,N/A,"Owner, Invitee",,GET,/invitations/{id},None,N/A
view,Invitation,Organization,None,,GET,/invitations/{id},User,Maintainer
resend,Invitation,Sandbox,"None, Invitee",,PATCH,/invitations/{id},Admin,N/A
resend,Invitation,N/A,Owner,,PATCH,/invitations/{id},Worker,N/A
resend,Invitation,Organization,"None, Invitee",,PATCH,/invitations/{id},User,Maintainer
delete,Invitation,Sandbox,"None, Invitee",,DELETE,/invitations/{id},Admin,N/A
delete,Invitation,N/A,Owner,,DELETE,/invitations/{id},Worker,N/A
delete,Invitation,Organization,"None, Invitee",,DELETE,/invitations/{id},User,Maintainer
accept,Invitation,N/A,None,,PATCH,/invitations/{id},Admin,N/A
accept,Invitation,N/A,Invitee,,PATCH,/invitations/{id},None,N/A