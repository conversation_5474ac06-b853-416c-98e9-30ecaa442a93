# Generated by Django 4.2.16 on 2024-10-30 12:03
from django.conf import settings
from django.db import migrations

BUSINESS_GROUP_NAME = "business"
USER_GROUP_NAME = "user"


def delete_business_group(apps, schema_editor):
    Group = apps.get_model("auth", "Group")
    User = apps.get_model(settings.AUTH_USER_MODEL)

    if user_group := Group.objects.filter(name=USER_GROUP_NAME).first():
        user_group.user_set.add(*User.objects.filter(groups__name=BUSINESS_GROUP_NAME))

    Group.objects.filter(name=BUSINESS_GROUP_NAME).delete()


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RunPython(
            delete_business_group,
            reverse_code=migrations.RunPython.noop,
        ),
    ]
