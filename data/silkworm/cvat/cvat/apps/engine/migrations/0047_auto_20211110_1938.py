# Generated by Django 3.2.8 on 2021-11-10 19:38

import django.db.models.deletion
from django.db import migrations, models

import cvat.apps.engine.models


class Migration(migrations.Migration):

    dependencies = [
        ("organizations", "0001_initial"),
        ("engine", "0046_data_sorting_method"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="issue",
            name="review",
        ),
        migrations.RemoveField(
            model_name="job",
            name="reviewer",
        ),
        migrations.AddField(
            model_name="cloudstorage",
            name="organization",
            field=models.ForeignKey(
                blank=True,
                default=None,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="cloudstorages",
                to="organizations.organization",
            ),
        ),
        migrations.AddField(
            model_name="job",
            name="stage",
            field=models.CharField(
                choices=[
                    ("annotation", "ANNOTATION"),
                    ("validation", "VALIDATION"),
                    ("acceptance", "ACCEPTANCE"),
                ],
                default=cvat.apps.engine.models.StageChoice["ANNOTATION"],
                max_length=32,
            ),
        ),
        migrations.AddField(
            model_name="job",
            name="state",
            field=models.CharField(
                choices=[
                    ("new", "NEW"),
                    ("in progress", "IN_PROGRESS"),
                    ("completed", "COMPLETED"),
                    ("rejected", "REJECTED"),
                ],
                default=cvat.apps.engine.models.StateChoice["NEW"],
                max_length=32,
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="organization",
            field=models.ForeignKey(
                blank=True,
                default=None,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="projects",
                to="organizations.organization",
            ),
        ),
        migrations.AddField(
            model_name="task",
            name="organization",
            field=models.ForeignKey(
                blank=True,
                default=None,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="tasks",
                to="organizations.organization",
            ),
        ),
        migrations.DeleteModel(
            name="Review",
        ),
    ]
