# Generated by Django 4.2.21 on 2025-07-07 09:30

import django.db.models.deletion
from django.db import connection, migrations, models
from django.db.models import F, OuterRef, Subquery


def set_attributeval_job_id(apps, schema_editor):
    LabeledImage = apps.get_model("engine", "LabeledImage")
    LabeledShape = apps.get_model("engine", "LabeledShape")
    LabeledTrack = apps.get_model("engine", "LabeledTrack")
    TrackedShape = apps.get_model("engine", "TrackedShape")

    LabeledImageAttributeVal = apps.get_model("engine", "LabeledImageAttributeVal")
    LabeledShapeAttributeVal = apps.get_model("engine", "LabeledShapeAttributeVal")
    LabeledTrackAttributeVal = apps.get_model("engine", "LabeledTrackAttributeVal")
    TrackedShapeAttributeVal = apps.get_model("engine", "TrackedShapeAttributeVal")

    LabeledImageAttributeVal.objects.annotate(
        related_job_id=Subquery(
            LabeledImage.objects.filter(id=OuterRef("image_id")).values("job_id")[:1]
        )
    ).update(job_id=F("related_job_id"))

    LabeledShapeAttributeVal.objects.annotate(
        related_job_id=Subquery(
            LabeledShape.objects.filter(id=OuterRef("shape_id")).values("job_id")[:1]
        )
    ).update(job_id=F("related_job_id"))

    LabeledTrackAttributeVal.objects.annotate(
        related_job_id=Subquery(
            LabeledTrack.objects.filter(id=OuterRef("track_id")).values("job_id")[:1]
        )
    ).update(job_id=F("related_job_id"))

    TrackedShapeAttributeVal.objects.annotate(
        related_job_id=Subquery(
            TrackedShape.objects.filter(id=OuterRef("shape_id")).values("track__job_id")[:1]
        )
    ).update(job_id=F("related_job_id"))


class Migration(migrations.Migration):

    dependencies = [
        ("engine", "0091_profile_last_activity_date"),
    ]

    operations = [
        migrations.AddField(
            model_name="labeledimageattributeval",
            name="job",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                to="engine.job",
            ),
        ),
        migrations.AddField(
            model_name="labeledshapeattributeval",
            name="job",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                to="engine.job",
            ),
        ),
        migrations.AddField(
            model_name="labeledtrackattributeval",
            name="job",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                to="engine.job",
            ),
        ),
        migrations.AddField(
            model_name="trackedshapeattributeval",
            name="job",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                to="engine.job",
            ),
        ),
        migrations.RunPython(set_attributeval_job_id, reverse_code=migrations.RunPython.noop),
        migrations.AlterField(
            model_name="labeledimageattributeval",
            name="job",
            field=models.ForeignKey(
                null=False,
                on_delete=django.db.models.deletion.DO_NOTHING,
                to="engine.job",
            ),
        ),
        migrations.AlterField(
            model_name="labeledshapeattributeval",
            name="job",
            field=models.ForeignKey(
                null=False,
                on_delete=django.db.models.deletion.DO_NOTHING,
                to="engine.job",
            ),
        ),
        migrations.AlterField(
            model_name="labeledtrackattributeval",
            name="job",
            field=models.ForeignKey(
                null=False,
                on_delete=django.db.models.deletion.DO_NOTHING,
                to="engine.job",
            ),
        ),
        migrations.AlterField(
            model_name="trackedshapeattributeval",
            name="job",
            field=models.ForeignKey(
                null=False,
                on_delete=django.db.models.deletion.DO_NOTHING,
                to="engine.job",
            ),
        ),
    ]
