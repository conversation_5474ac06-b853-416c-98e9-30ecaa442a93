# Generated by Django 4.2.20 on 2025-05-01 11:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("engine", "0088_consensus_jobs"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="labeledimage",
            name="source",
            field=models.CharField(
                choices=[
                    ("auto", "AUTO"),
                    ("semi-auto", "SEMI_AUTO"),
                    ("manual", "MANUAL"),
                    ("file", "FILE"),
                    ("consensus", "CONSENSUS"),
                ],
                default="manual",
                max_length=16,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="labeledshape",
            name="source",
            field=models.CharField(
                choices=[
                    ("auto", "AUTO"),
                    ("semi-auto", "SEMI_AUTO"),
                    ("manual", "MANUAL"),
                    ("file", "FILE"),
                    ("consensus", "CONSENSUS"),
                ],
                default="manual",
                max_length=16,
                null=True,
            ),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="labeledtrack",
            name="source",
            field=models.Char<PERSON><PERSON>(
                choices=[
                    ("auto", "AUTO"),
                    ("semi-auto", "SEMI_AUTO"),
                    ("manual", "<PERSON><PERSON>AL"),
                    ("file", "FILE"),
                    ("consensus", "CONSENSUS"),
                ],
                default="manual",
                max_length=16,
                null=True,
            ),
        ),
    ]
