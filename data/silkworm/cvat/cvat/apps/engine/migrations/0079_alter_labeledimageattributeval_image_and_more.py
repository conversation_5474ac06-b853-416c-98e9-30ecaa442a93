# Generated by Django 4.2.13 on 2024-07-09 11:08

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("engine", "0078_alter_cloudstorage_credentials"),
    ]

    operations = [
        migrations.AlterField(
            model_name="labeledimageattributeval",
            name="image",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="attributes",
                related_query_name="attribute",
                to="engine.labeledimage",
            ),
        ),
        migrations.AlterField(
            model_name="labeledshapeattributeval",
            name="shape",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="attributes",
                related_query_name="attribute",
                to="engine.labeledshape",
            ),
        ),
        migrations.AlterField(
            model_name="labeledtrackattributeval",
            name="track",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="attributes",
                related_query_name="attribute",
                to="engine.labeledtrack",
            ),
        ),
        migrations.AlterField(
            model_name="trackedshapeattributeval",
            name="shape",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="attributes",
                related_query_name="attribute",
                to="engine.trackedshape",
            ),
        ),
    ]
