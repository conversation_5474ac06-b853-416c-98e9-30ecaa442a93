# Generated by Django 4.2.14 on 2024-07-22 07:27

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("engine", "0081_job_assignee_updated_date_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="labeledimage",
            name="job",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.DO_NOTHING, to="engine.job"
            ),
        ),
        migrations.AlterField(
            model_name="labeledimageattributeval",
            name="image",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="attributes",
                related_query_name="attribute",
                to="engine.labeledimage",
            ),
        ),
        migrations.AlterField(
            model_name="labeledshape",
            name="job",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.DO_NOTHING, to="engine.job"
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="labeledshape",
            name="parent",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="elements",
                to="engine.labeledshape",
            ),
        ),
        migrations.AlterField(
            model_name="labeledshapeattributeval",
            name="shape",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="attributes",
                related_query_name="attribute",
                to="engine.labeledshape",
            ),
        ),
        migrations.AlterField(
            model_name="labeledtrack",
            name="job",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.DO_NOTHING, to="engine.job"
            ),
        ),
        migrations.AlterField(
            model_name="labeledtrack",
            name="parent",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="elements",
                to="engine.labeledtrack",
            ),
        ),
        migrations.AlterField(
            model_name="labeledtrackattributeval",
            name="track",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="attributes",
                related_query_name="attribute",
                to="engine.labeledtrack",
            ),
        ),
        migrations.AlterField(
            model_name="trackedshapeattributeval",
            name="shape",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="attributes",
                related_query_name="attribute",
                to="engine.trackedshape",
            ),
        ),
    ]
