# Copyright (C) 2018-2022 Intel Corporation
#
# SPDX-License-Identifier: MIT

# Generated by Django 2.0.3 on 2018-06-04 11:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "engine",
            "0002_labeledpoints_labeledpointsattributeval_labeledpolygon_labeledpolygonattributeval_labeledpolyline_la",
        ),
    ]

    operations = [
        migrations.AddField(
            model_name="objectpath",
            name="shapes",
            field=models.CharField(default="boxes", max_length=10),
        ),
    ]
