# Generated by Django 4.2.13 on 2024-07-12 19:06

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("quality_control", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="qualityreport",
            name="assignee",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="quality_reports",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
