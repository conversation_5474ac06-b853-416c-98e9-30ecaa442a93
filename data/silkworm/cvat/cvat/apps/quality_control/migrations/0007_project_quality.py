# Generated by Django 4.2.18 on 2025-02-17 18:01

from datetime import datetime, timezone

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


def add_timestamps_to_settings(apps, schema_editor):
    QualitySettings = apps.get_model("quality_control", "QualitySettings")
    Task = apps.get_model("engine", "Task")

    # Synchronize settings and task timestamps for consistency
    (
        QualitySettings.objects.filter(task__isnull=False)
        .annotate(
            task_created_date=models.Subquery(
                Task.objects.filter(id=models.OuterRef("task_id")).values_list(
                    "created_date", flat=True
                )[:1]
            ),
        )
        .update(
            created_date=models.F("task_created_date"),
            updated_date=datetime.now(timezone.utc),
        )
    )


def set_project_report_gt_update_dates(apps, schema_editor):
    QualityReport = apps.get_model("quality_control", "QualityReport")
    QualityReport.objects.filter(project__isnull=False, gt_last_updated__isnull=True).update(
        gt_last_updated=datetime.now(timezone.utc)
    )


def init_settings_in_existing_projects(apps, schema_editor):
    Project = apps.get_model("engine", "Project")
    QualitySettings = apps.get_model("quality_control", "QualitySettings")

    projects_without_settings = Project.objects.filter(quality_settings__isnull=True).distinct()
    QualitySettings.objects.bulk_create(
        [
            QualitySettings(
                project=p,
                compare_attributes=True,
                iou_threshold=0.4,
                oks_sigma=0.09,
                line_thickness=0.01,
                low_overlap_threshold=0.8,
                point_size_base="group_bbox_size",
                compare_line_orientation=True,
                line_orientation_threshold=0.1,
                compare_groups=True,
                group_match_threshold=0.5,
                check_covered_annotations=True,
                object_visibility_threshold=0.05,
                panoptic_comparison=True,
                empty_is_annotated=False,
                target_metric="accuracy",
                target_metric_threshold=0.7,
                max_validations_per_job=0,
            )
            for p in projects_without_settings
        ],
        batch_size=10000,
    )

    # Preserve existing quality settings for tasks in projects
    QualitySettings.objects.filter(task__project__isnull=False).update(inherit=False)


def remove_project_settings(apps, schema_editor):
    QualitySettings = apps.get_model("quality_control", "QualitySettings")
    QualitySettings.objects.filter(project__isnull=False).delete()


def init_m2m_for_report_parent(apps, schema_editor):
    QualityReport = apps.get_model("quality_control", "QualityReport")

    ThroughModel = QualityReport.parents.through
    ThroughModel.objects.bulk_create(
        (
            ThroughModel(from_qualityreport_id=id, to_qualityreport_id=parent_id)
            for id, parent_id in (
                QualityReport.objects.filter(parent__isnull=False)
                .values_list("id", "parent_id")
                .iterator(chunk_size=1000)
            )
        ),
        batch_size=1000,
    )


def revert_m2m_for_report_parent(apps, schema_editor):
    QualityReport = apps.get_model("quality_control", "QualityReport")

    if top_report_uses := (
        QualityReport.objects.annotate(use_count=models.aggregates.Count("parents"))
        .order_by("-use_count")
        .filter(use_count__gt=1)
        .values_list("id", "job_id", "task_id", "use_count")[:10]
    ):
        raise Exception(
            "Can't run backward migration: "
            "there are QualityReport objects with more than 1 parent report. "
            "Top QualityReport uses: {}".format(
                ", ".join(
                    f"\n\t{id=} ({job_id=}, {task_id=}): {count}"
                    for id, job_id, task_id, count in top_report_uses
                )
            )
        )

    ThroughModel = QualityReport.parents.through

    (
        QualityReport.objects.annotate(use_count=models.aggregates.Count("parents"))
        .filter(use_count__gt=0)
        .update(
            parent_id=models.Subquery(
                ThroughModel.objects.filter(
                    from_qualityreport_id=models.OuterRef("id")
                ).values_list("to_qualityreport_id", flat=True)[:1]
            )
        )
    )


def remove_project_report_relations(apps, schema_editor):
    # Django m2m seem to have no on_delete=CASCADE support, so we remove relations manually
    QualityReport = apps.get_model("quality_control", "QualityReport")
    ThroughModel = QualityReport.parents.through
    ThroughModel.objects.filter(to_qualityreport__project__isnull=False).delete()
    ThroughModel.objects.filter(from_qualityreport__project__isnull=False).delete()


class Migration(migrations.Migration):

    dependencies = [
        ("engine", "0088_consensus_jobs"),
        ("quality_control", "0006_rename_match_empty_frames_qualitysettings_empty_is_annotated"),
    ]

    operations = [
        migrations.AddField(
            model_name="qualitysettings",
            name="created_date",
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="qualitysettings",
            name="updated_date",
            field=models.DateTimeField(auto_now=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.RunPython(
            add_timestamps_to_settings,
            reverse_code=migrations.RunPython.noop,
        ),
        migrations.AddField(
            model_name="qualityreport",
            name="parents",
            field=models.ManyToManyField(blank=True, to="quality_control.qualityreport"),
        ),
        migrations.RunPython(
            init_m2m_for_report_parent,
            reverse_code=revert_m2m_for_report_parent,
        ),
        migrations.RemoveField(
            model_name="qualityreport",
            name="parent",
        ),
        migrations.AlterField(
            model_name="qualityreport",
            name="parents",
            field=models.ManyToManyField(
                blank=True,
                to="quality_control.qualityreport",
                name="parents",
                related_name="children",
            ),
        ),
        migrations.AddField(
            model_name="qualityreport",
            name="project",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="quality_reports",
                to="engine.project",
            ),
        ),
        migrations.RunPython(
            migrations.RunPython.noop,
            reverse_code=remove_project_report_relations,
        ),
        migrations.AddField(
            model_name="qualitysettings",
            name="inherit",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="qualitysettings",
            name="project",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="quality_settings",
                to="engine.project",
            ),
        ),
        migrations.AlterField(
            model_name="qualityreport",
            name="gt_last_updated",
            field=models.DateTimeField(null=True),
        ),
        migrations.RunPython(
            migrations.RunPython.noop,
            reverse_code=set_project_report_gt_update_dates,
        ),
        migrations.AlterField(
            model_name="qualitysettings",
            name="task",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="quality_settings",
                to="engine.task",
            ),
        ),
        migrations.RunPython(
            init_settings_in_existing_projects,
            reverse_code=remove_project_settings,
        ),
    ]
