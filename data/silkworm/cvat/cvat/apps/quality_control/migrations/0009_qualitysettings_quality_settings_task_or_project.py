# Generated by Django 4.2.18 on 2025-03-27 11:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("quality_control", "0008_qualitysettings_job_filter"),
    ]

    operations = [
        migrations.AddConstraint(
            model_name="qualitysettings",
            constraint=models.CheckConstraint(
                check=models.Q(
                    models.Q(("project_id__isnull", True), ("task_id__isnull", False)),
                    models.Q(("project_id__isnull", False), ("task_id__isnull", True)),
                    _connector="OR",
                ),
                name="quality_settings_task_or_project",
            ),
        ),
    ]
