# Generated by Django 4.2.15 on 2024-08-21 13:56

from django.db import migrations, models

import cvat.apps.quality_control.models


class Migration(migrations.Migration):

    dependencies = [
        ("quality_control", "0002_qualityreport_assignee"),
    ]

    operations = [
        migrations.AddField(
            model_name="qualityreport",
            name="assignee_last_updated",
            field=models.DateTimeField(null=True),
        ),
        migrations.AddField(
            model_name="qualitysettings",
            name="max_validations_per_job",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name="qualitysettings",
            name="target_metric",
            field=models.CharField(
                choices=[
                    ("accuracy", "ACCURACY"),
                    ("precision", "PRECISION"),
                    ("recall", "RECALL"),
                ],
                default=cvat.apps.quality_control.models.QualityTargetMetricType["ACCURACY"],
                max_length=32,
            ),
        ),
        migrations.AddField(
            model_name="qualitysettings",
            name="target_metric_threshold",
            field=models.FloatField(default=0.7),
        ),
    ]
