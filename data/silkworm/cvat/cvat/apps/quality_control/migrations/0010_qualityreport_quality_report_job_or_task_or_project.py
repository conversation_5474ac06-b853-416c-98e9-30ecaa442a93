# Generated by Django 4.2.20 on 2025-04-28 16:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("quality_control", "0009_qualitysettings_quality_settings_task_or_project"),
    ]

    operations = [
        migrations.AddConstraint(
            model_name="qualityreport",
            constraint=models.CheckConstraint(
                check=models.Q(
                    models.Q(
                        ("job_id__isnull", False),
                        ("project_id__isnull", True),
                        ("task_id__isnull", True),
                    ),
                    models.Q(
                        ("job_id__isnull", True),
                        ("project_id__isnull", True),
                        ("task_id__isnull", False),
                    ),
                    models.Q(
                        ("job_id__isnull", True),
                        ("project_id__isnull", False),
                        ("task_id__isnull", True),
                    ),
                    _connector="OR",
                ),
                name="quality_report_job_or_task_or_project",
            ),
        ),
    ]
