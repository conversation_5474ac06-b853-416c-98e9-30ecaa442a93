{"name": "cvat-canvas", "version": "2.20.10", "type": "module", "description": "Part of Computer Vision Annotation Tool which presents its canvas library", "main": "src/canvas.ts", "scripts": {"build": "tsc && webpack --config ./webpack.config.cjs"}, "author": "CVAT.ai", "license": "MIT", "browserslist": ["Chrome >= 99", "Firefox >= 110", "not IE 11", "> 2%"], "dependencies": {"@types/polylabel": "^1.0.5", "polylabel": "^1.1.0", "svg.draggable.js": "2.2.2", "svg.draw.js": "^2.0.4", "svg.js": "2.7.1", "svg.resize.js": "1.4.3", "svg.select.js": "3.0.1"}}