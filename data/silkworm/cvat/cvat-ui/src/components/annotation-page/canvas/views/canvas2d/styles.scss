// Copyright (C) CVAT.ai Corporation
//
// SPDX-License-Identifier: MIT

@import 'base';

.cvat-image-setups-filters {
    margin-bottom: $grid-unit-size * 3;
}

.cvat-canvas-hints-container {
    filter: none;
    position: absolute;
    top: $grid-unit-size;
    right: $grid-unit-size;
    width: $grid-unit-size * 32;
    background: rgba(0, 0, 0, 60%);
    color: white;
    padding: $grid-unit-size;
    border-radius: $border-radius-base;
    transition: filter 0.15s linear;
    overflow: hidden;
    z-index: 100;
    pointer-events: none;

    &.cvat-canvas-hints-container-disabled {
        filter: blur(10px);
        visibility: hidden;
    }

    .cvat-canvas-hints-block {
        font-weight: normal;

        > ul {
            padding-inline-start: $grid-unit-size * 2;
            margin-bottom: 0;
        }

        &:not(:first-child) {
            margin-top: $grid-unit-size * 2;
            font-weight: lighter;
        }

        > span[role="img"] {
            margin-right: $grid-unit-size * 2;
        }

        > .ant-typography {
            color: white;
        }
    }

    .cvat-canvas-hints-hide-button {
        float: right;
        pointer-events: all;

        &:hover {
            transform: scale(1.1);
        }
    }
}
