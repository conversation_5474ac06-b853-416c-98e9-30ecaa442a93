# ===== Silkworm-11 keypoints, COCO format =====
dataset_type = 'TopDownCocoDataset'

data_root = 'data/silkworm/'

# ---------- keypoints & skeleton ----------
dataset_info = dict(
    dataset_name='silkworm11',
    keypoint_info={
        0:  dict(name='H1', id=0,  color=[255, 0, 0], type='upper', swap=''),
        1:  dict(name='T1', id=1,  color=[255, 0, 0], type='upper', swap=''),
        2:  dict(name='T2', id=2,  color=[255, 0, 0], type='upper', swap=''),
        3:  dict(name='T3', id=3,  color=[255, 0, 0], type='upper', swap=''),
        4:  dict(name='A2', id=4,  color=[255, 0, 0], type='lower', swap=''),
        5:  dict(name='A3', id=5,  color=[255, 0, 0], type='lower', swap=''),
        6:  dict(name='A4', id=6,  color=[255, 0, 0], type='lower', swap=''),
        7:  dict(name='A5', id=7,  color=[255, 0, 0], type='lower', swap=''),
        8:  dict(name='A6', id=8,  color=[255, 0, 0], type='lower', swap=''),
        9:  dict(name='A8', id=9,  color=[255, 0, 0], type='lower', swap=''),
        10: dict(name='A9', id=10, color=[255, 0, 0], type='lower', swap='')
    },
    skeleton_info={
        0: dict(link=('H1', 'T1'), color=[0, 255, 0]),
        1: dict(link=('T1', 'T2'), color=[0, 255, 0]),
        2: dict(link=('T2', 'T3'), color=[0, 255, 0]),
        3: dict(link=('T3', 'A2'), color=[0, 255, 0]),
        4: dict(link=('A2', 'A3'), color=[0, 255, 0]),
        5: dict(link=('A3', 'A4'), color=[0, 255, 0]),
        6: dict(link=('A4', 'A5'), color=[0, 255, 0]),
        7: dict(link=('A5', 'A6'), color=[0, 255, 0]),
        8: dict(link=('A6', 'A8'), color=[0, 255, 0]),
        9: dict(link=('A8', 'A9'), color=[0, 255, 0]),
    },
    joint_weights=[1.0] * 11,
    sigmas=[0.01] * 11,
    flip_pairs=[]
)
# -------------------------------------------

image_size   = [192, 256]   # (w, h)
heatmap_size = [48,  64]

# ---------------- pipelines ----------------
train_pipeline = [
    dict(type='LoadImage'),
    dict(type='GetBBoxCenterScale', padding=1.25),
    dict(type='RandomFlip', prob=0.5, direction='horizontal'),
    dict(type='RandomBBoxTransform',
         scale_factor=[0.7, 1.3], rotate_factor=40),
    dict(type='TopdownAffine', input_size=image_size),
    dict(type='GenerateTarget', encoding='MSRAHeatmap', sigma=2),
    dict(type='PackPoseInputs')
]

val_pipeline = [
    dict(type='LoadImage'),
    dict(type='GetBBoxCenterScale', padding=1.25),
    dict(type='TopdownAffine', input_size=image_size),
    dict(type='PackPoseInputs')
]
# -------------------------------------------

_common_cfg = dict(
    type=dataset_type,
    data_root=data_root,
    data_prefix=dict(img='images/'),
    data_cfg=dict(
        image_size=image_size,
        heatmap_size=heatmap_size,
        num_output_channels=11,
        num_joints=11,
        dataset_channel=[list(range(11))],
        inference_channel=list(range(11)),
    ),
    dataset_info=dataset_info,
)

train_dataloader = dict(
    batch_size=64,
    num_workers=4,
    dataset=dict(
        ann_file='train.json',
        pipeline=train_pipeline,
        **_common_cfg),
    sampler=dict(type='DefaultSampler', shuffle=True)
)

val_dataloader = dict(
    batch_size=64,
    num_workers=4,
    dataset=dict(
        ann_file='val.json',
        pipeline=val_pipeline,
        **_common_cfg),
    sampler=dict(type='DefaultSampler', shuffle=False)
)
test_dataloader = val_dataloader

val_evaluator  = dict(type='CocoMetric', ann_file=f'{data_root}val.json')
test_evaluator = val_evaluator