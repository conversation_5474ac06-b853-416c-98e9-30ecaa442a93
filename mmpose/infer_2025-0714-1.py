from mmpose.apis import init_model, inference_topdown
import cv2
import os

# --------------------------------------------------
# 1) パスを設定
# --------------------------------------------------
cfg  = 'configs/silkworm/td-hm_hrnet-w32_8xb64-210e_silkworm-256x192.py'
ckpt = 'work_dirs/hrnet_silkworm11/epoch_210.pth'
image_file = 'test1.jpg'                  # 推論したい画像
out_file   = 'test1_pose.jpg'             # 保存先

# --------------------------------------------------
# 2) モデルを読み込む
# --------------------------------------------------
model = init_model(cfg, ckpt, device='cuda:0')  # or 'cpu'

# --------------------------------------------------
# 3) 画像全体を bbox として与える
# --------------------------------------------------
img = cv2.imread(image_file)
if img is None:
    raise FileNotFoundError(image_file)

h, w = img.shape[:2]
person_results = [dict(bbox=[0, 0, w, h])]   # ★ bbox キーは必須

# --------------------------------------------------
# 4) 推論
# --------------------------------------------------
preds = inference_topdown(model, image_file, person_results)

# --------------------------------------------------
# 5) 可視化して保存
# --------------------------------------------------
model.visualize(
    image=image_file,
    data_samples=preds,
    out_file=out_file,
    thickness=2,
    radius=4,
    kpt_thr=0.1      # しきい値（必要なら調整）
)

print(f'result saved to: {os.path.abspath(out_file)}')